version: '3.8'
services:
  claude-relay:
    image: weishaw/claude-relay-service:latest
    container_name: claude-relay-service
    restart: unless-stopped
    ports:
      - "9755:3000"
    environment:
      - JWT_SECRET=${JWT_SECRET}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - REDIS_HOST=redis
      - ADMIN_USERNAME=${ADMIN_USERNAME:-}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD:-}
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    depends_on:
      - redis

  redis:
    image: redis:7-alpine
    container_name: claude-relay-redis
    restart: unless-stopped
    volumes:
      - redis_data:/data

volumes:
  redis_data:
