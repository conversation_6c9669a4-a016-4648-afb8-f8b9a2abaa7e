# Claude中转服务核心逻辑说明

## 🎯 项目目标
让普通用户可以通过API Key使用Claude，但实际上是通过模拟Claude Code官方客户端来获得更好的服务。

## 🔄 传统方式 vs 当前方式

### 传统nginx代理方式
```
用户请求 → nginx中转服务器 → api.anthropic.com
```
- **简单粗暴**：只是改变请求地址
- **透明转发**：不修改请求内容
- **限制多**：受API Key各种限制

### 当前项目的智能方式
```
用户请求 → 智能中转服务 → 伪装成Claude Code → claude.ai
```
- **智能伪装**：模拟官方Claude Code客户端
- **内容改写**：修改请求让它看起来像官方客户端发的
- **权限更高**：获得Claude Code用户的高级权限

## 🎭 核心替换逻辑

### 第1步：接收用户请求
```javascript
// 用户发送普通的API请求
POST /v1/messages
{
  "model": "claude-3-sonnet",
  "messages": [{"role": "user", "content": "你好"}]
}
```

### 第2步：获取真实身份令牌
```javascript
// 使用OAuth获取真实的Claude账号令牌
const accessToken = await claudeAccountService.getValidAccessToken(accountId);
// 如果令牌过期了，自动刷新
if (isExpired) {
  await this.refreshAccountToken(accountId);
}
```

### 第3步：伪装成Claude Code客户端

#### 3.1 添加Claude Code系统提示词
```javascript
// 在请求前面加上Claude Code的特殊提示词
const claudeCodePrompt = {
  type: 'text',
  text: '你是Augment Agent，由Augment Code开发的AI助手...',
  cache_control: { type: 'ephemeral' }
};
// 把这个提示词放在最前面
processedBody.system = [claudeCodePrompt, ...用户原来的提示词];
```

#### 3.2 伪造Claude Code的Headers
```javascript
// 模拟Claude Code客户端的请求头
const claudeCodeHeaders = {
  'user-agent': 'claude-cli/1.0.57 (external, cli)',
  'x-app': 'cli',
  'x-stainless-package-version': '0.55.1',
  'anthropic-dangerous-direct-browser-access': 'true'
};
```

### 第4步：发送伪装后的请求
```javascript
// 发送到Claude官方API，但看起来像是Claude Code发的
const response = await https.request({
  hostname: 'api.anthropic.com',
  headers: {
    'Authorization': `Bearer ${真实OAuth令牌}`,
    ...claudeCodeHeaders
  }
}, 伪装后的请求体);
```

### 第5步：返回响应给用户
```javascript
// 把Claude的响应原样返回给用户
response.pipe(用户的响应流);
```

## 🔑 关键技术点

### 1. OAuth令牌管理
- **自动获取**：通过OAuth流程获取真实Claude账号的访问令牌
- **自动刷新**：令牌快过期时自动刷新，用户无感知
- **多账号池**：管理多个Claude账号，轮流使用避免限流

### 2. 请求伪装技术
- **系统提示词注入**：让Claude以为这是Claude Code的请求
- **Headers模拟**：完全模拟Claude Code客户端的请求头
- **请求体改写**：调整请求格式符合Claude Code的要求

### 3. 会话管理
- **粘性会话**：同一个对话尽量使用同一个Claude账号
- **负载均衡**：多个账号之间智能分配请求
- **限流处理**：账号被限流时自动切换到其他账号

## 🎪 为什么这样做？

### 优势对比

| 特性 | 普通API Key | Claude Code模拟 |
|------|-------------|-----------------|
| 使用限制 | 严格限制 | 更宽松 |
| 功能权限 | 基础功能 | 完整功能 |
| 响应速度 | 一般 | 更快 |
| 稳定性 | 容易被限流 | 更稳定 |

### 实际效果
- **用户体验**：用户只需要一个API Key，就能享受Claude Code级别的服务
- **服务稳定**：多账号池确保服务不中断
- **功能完整**：获得Claude的所有高级功能

## 🔧 技术架构

```
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│   用户请求   │───▶│  智能中转服务  │───▶│  Claude官方  │
│ (API Key)   │    │              │    │ (OAuth令牌) │
└─────────────┘    └──────────────┘    └─────────────┘
                          │
                          ▼
                   ┌──────────────┐
                   │   核心逻辑    │
                   │ • OAuth管理  │
                   │ • 请求伪装   │
                   │ • 会话管理   │
                   │ • 账号调度   │
                   └──────────────┘
```

## 🎯 总结

这个项目的核心就是**"狸猫换太子"**：
1. **接收**用户的普通API请求
2. **伪装**成Claude Code官方客户端
3. **使用**真实OAuth令牌访问Claude
4. **返回**高质量的响应给用户

通过这种方式，用户可以用简单的API Key享受到Claude Code级别的服务，而不需要自己去处理复杂的OAuth流程和客户端伪装。
